import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

// Helper function to generate a UUID from a string
const generateUUIDFromString = (str: string): string => {
  // Remove non-alphanumeric characters and convert to lowercase
  const sanitizedStr = str.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
  
  // Pad/truncate to 32 characters
  // This is not cryptographically secure but works for demo purposes
  const padded = sanitizedStr.padEnd(32, '0').substring(0, 32);
  
  // Format as UUID
  return `${padded.substring(0, 8)}-${padded.substring(8, 12)}-${padded.substring(12, 16)}-${padded.substring(16, 20)}-${padded.substring(20, 32)}`;
};

export const usePlaytimeTracker = (gameId: number, userId?: string, actualUsername?: string | null) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const startTimeRef = useRef<number>(Date.now());

  const startSession = async () => {
    if (!userId) {
      console.log('No user ID provided, skipping playtime tracking');
      return;
    }

    try {
      console.log('Starting play session for game:', gameId, 'user:', userId, 'actualUsername:', actualUsername);
      
      const userUUID = generateUUIDFromString(userId);
      console.log('Generated UUID for user:', userUUID);
      
      const { data, error } = await supabase.rpc('start_play_session', {
        p_game_id: gameId,
        p_user_id: userUUID,
        p_actual_username: actualUsername // Use the passed actualUsername
      } as any); 

      if (error) {
        console.error('Error starting play session:', error);
        if (error.message && error.message.includes('invalid input syntax for type uuid')) {
          console.error('Problematic UUID generated:', userUUID, 'from input:', userId);
        }
        return;
      }

      console.log('Play session started successfully, session ID:', data);
      setSessionId(data);
      setIsTracking(true);
      startTimeRef.current = Date.now();
    } catch (error) {
      console.error('Error starting session:', error);
    }
  };

  const endSession = async () => {
    if (!sessionId || !isTracking || !userId) {
      console.log('Cannot end session - missing requirements:', { sessionId, isTracking, userId });
      return;
    }

    try {
      console.log('Ending play session:', sessionId);
      
      const userUUID = generateUUIDFromString(userId); // Ensure consistent UUID generation
      
      const { error } = await supabase.rpc('end_play_session', {
        p_session_id: sessionId,
        p_user_id: userUUID
      } as any); 

      if (error) {
        console.error('Error ending play session:', error);
        if (error.message && error.message.includes('invalid input syntax for type uuid')) {
          console.error('Problematic UUID generated for endSession:', userUUID, 'from input:', userId);
        }
        return;
      }

      const sessionDuration = Math.floor((Date.now() - startTimeRef.current) / 1000);
      console.log(`Ended play session. Duration: ${sessionDuration} seconds`);
      
      setSessionId(null);
      setIsTracking(false);
    } catch (error) {
      console.error('Error ending session:', error);
    }
  };

  // Auto-start session when hook is initialized
  useEffect(() => {
    if (userId && gameId) {
      console.log('Auto-starting session for user:', userId, 'game:', gameId, 'actualUsername:', actualUsername);
      startSession();
    }

    // Cleanup function to end session when component unmounts
    return () => {
      if (sessionId && isTracking && userId) {
        console.log('Component unmounting, ending session');
        const userUUID = generateUUIDFromString(userId); // Ensure consistent UUID generation
        // Don't await here as the component is unmounting
        supabase.rpc('end_play_session', {
          p_session_id: sessionId,
          p_user_id: userUUID
        } as any);
      }
    };
  }, [gameId, userId, actualUsername]); // Add actualUsername to dependencies

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isTracking) {
        console.log('Page hidden, ending session');
        endSession();
      } else if (!document.hidden && !isTracking && userId && sessionId === null) {
        if (gameId) {
          console.log('Page visible, starting new session with actualUsername:', actualUsername);
          startSession();
        }
      }
    };

    const handleBeforeUnload = () => {
      if (sessionId && isTracking && userId) {
        console.log('Page unloading, ending session');
        const userUUID = generateUUIDFromString(userId); // Ensure consistent UUID generation
        // Use sendBeacon for more reliable tracking on page unload
        // Ensure your server-side endpoint for /api/end-session is set up if you use this.
        // For now, relying on Supabase direct call.
        // navigator.sendBeacon('/api/end-session', JSON.stringify({
        //   sessionId,
        //   userId: userUUID
        // }));
         supabase.rpc('end_play_session', { // Use direct rpc for simplicity for now
          p_session_id: sessionId,
          p_user_id: userUUID
        } as any);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [sessionId, isTracking, userId, gameId, actualUsername]); // Add actualUsername to dependencies

  return { isTracking, sessionId };
};
