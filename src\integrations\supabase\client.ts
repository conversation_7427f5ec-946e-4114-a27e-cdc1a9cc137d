// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fjfnvbxmrqtqolitjyst.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqZm52YnhtcnF0cW9saXRqeXN0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4OTE2NDYsImV4cCI6MjA2NTQ2NzY0Nn0.DQdKQQcJRlirhMNfjgYt-D2DXgJwat91xXXcO6CIlrU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);