
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { AspectRatio } from "@/components/ui/aspect-ratio"; // Added import
import { ArrowLeft, Maximize2, Minimize2, Clock, CheckCircle, XCircle } from 'lucide-react';
import { useState, useEffect } from 'react';
import { usePlaytimeTracker } from '@/hooks/usePlaytimeTracker';

const games = [
  {
    id: 1,
    title: "Crazy Dummy Swing Multiplayer",
    description: "Swing through levels with physics-based gameplay in this multiplayer adventure!",
    coverImage: "https://imgs.crazygames.com/crazy-dummy-swing-multiplayer_2x3/20250506035621/crazy-dummy-swing-multiplayer_2x3-cover?metadata=none&quality=85&width=273&fit=crop",
    embedUrl: "https://www.crazygames.com/embed/crazy-dummy-swing-multiplayer"
  },
  {
    id: 2,
    title: "Geoguessr",
    description: "Guess where you are in this addictive geography game!",
    coverImage: "https://imgs.crazygames.com/worldguessr_16x9/20241018082520/worldguessr_16x9-cover?metadata=none&quality=85&width=400&fit=cover",
    embedUrl: "https://www.crazygames.com/embed/worldguessr"
  }
];

const GameDetail = () => {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [userInfo, setUserInfo] = useState<{ username?: string; id?: string } | null>(null);
  const [playtimeSeconds, setPlaytimeSeconds] = useState(0);

  const game = games.find(g => g.id === parseInt(gameId || ''));
  
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const username = urlParams.get('username');
    const id = urlParams.get('id');
    
    if (username && id) {
      setUserInfo({ username, id });
    }
  }, [location]);

  // Pass userInfo.username as the third argument to usePlaytimeTracker
  const { isTracking } = usePlaytimeTracker(
    parseInt(gameId || '0'), 
    userInfo?.id,
    userInfo?.username // Pass the actual username here
  );

  // Timer for playtime display
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isTracking) {
      interval = setInterval(() => {
        setPlaytimeSeconds(prev => prev + 1);
      }, 1000);
    } else {
      setPlaytimeSeconds(0);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isTracking]);

  useEffect(() => {
    // Check if fullscreen parameter is in URL
    const urlParams = new URLSearchParams(location.search);
    if (urlParams.get('fullscreen') === 'true') {
      setIsFullscreen(true);
    }
  }, [location]);

  if (!game) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#0C0D14' }}>
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Game Not Found</h1>
          <Button onClick={() => navigate('/')} className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleBackToHome = () => {
    // Preserve URL parameters when going back to home
    const currentParams = new URLSearchParams(location.search);
    // Remove fullscreen parameter for home page
    currentParams.delete('fullscreen');
    const homeUrl = currentParams.toString() ? `/?${currentParams.toString()}` : '/';
    navigate(homeUrl);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#0C0D14' }}>
      {!isFullscreen && (
        <div className="bg-gray-800/50 border-b border-gray-700 p-4">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                onClick={handleBackToHome}
                className="text-white hover:bg-gray-700 rounded-full p-2"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">{game.title}</h1>
                <p className="text-gray-400 text-sm">{game.description}</p>
                
                {/* Playtime tracking status */}
                <div className="flex items-center gap-4 mt-2">
                  {userInfo ? (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-300">Player: {userInfo.username}</span>
                      {isTracking ? (
                        <div className="flex items-center gap-1">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <Clock className="w-4 h-4 text-green-400" />
                          <span className="text-green-400 text-sm font-mono">
                            {formatTime(playtimeSeconds)}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <XCircle className="w-4 h-4 text-red-400" />
                          <span className="text-red-400 text-sm">Tracking inactive</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-400 text-xs">Add ?username=YourName&id=123 to URL to track playtime</p>
                  )}
                </div>
              </div>
            </div>
            <Button 
              variant="ghost" 
              onClick={toggleFullscreen}
              className="text-white hover:bg-gray-700 rounded-full p-2"
            >
              <Maximize2 className="w-5 h-5" />
            </Button>
          </div>
        </div>
      )}

      {/* Game Container - Refactored for AspectRatio */}
      {isFullscreen ? (
        // Fullscreen Game View
        <div className="fixed inset-0 z-50 bg-black flex items-center justify-center">
          <div className="w-full h-full relative">
            {/* Fullscreen UI elements: Minimize Button and Playtime Indicator */}
            <div className="absolute top-4 right-4 z-10 flex gap-2">
              {userInfo && isTracking && (
                <div className="bg-black/70 rounded-lg px-3 py-1 flex items-center gap-2">
                  <Clock className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm font-mono">
                    {formatTime(playtimeSeconds)}
                  </span>
                </div>
              )}
              <Button 
                variant="ghost" 
                onClick={toggleFullscreen}
                className="text-white hover:bg-gray-700 rounded-full p-2 bg-black/50"
              >
                <Minimize2 className="w-5 h-5" />
              </Button>
            </div>
            {/* Fullscreen iframe */}
            <iframe
              src={game.embedUrl}
              className="w-full h-full border-0"
              allowFullScreen
              allow="gamepad; microphone; camera"
              title={game.title}
            />
          </div>
        </div>
      ) : (
        // Non-Fullscreen Game View (within page layout)
        <div className="container mx-auto p-4">
          <div className="w-full max-w-6xl mx-auto">
            <AspectRatio ratio={16 / 9} className="bg-gray-800 rounded-2xl overflow-hidden shadow-2xl">
              <iframe
                src={game.embedUrl}
                className="w-full h-full border-0"
                allowFullScreen
                allow="gamepad; microphone; camera"
                title={game.title}
              />
            </AspectRatio>
          </div>
        </div>
      )}

      {!isFullscreen && (
        <div className="container mx-auto px-4 pb-8">
          <div className="max-w-6xl mx-auto">
            <div className="mt-6 p-6 bg-gray-800/50 rounded-2xl border border-gray-700">
              <h2 className="text-xl font-bold text-white mb-3">About This Game</h2>
              <p className="text-gray-300 leading-relaxed">{game.description}</p>
              
              <div className="mt-6 flex flex-wrap gap-4">
                <div className="bg-purple-600/20 rounded-lg px-4 py-2">
                  <span className="text-purple-400 font-medium">Multiplayer</span>
                </div>
                <div className="bg-blue-600/20 rounded-lg px-4 py-2">
                  <span className="text-blue-400 font-medium">Browser Game</span>
                </div>
                <div className="bg-green-600/20 rounded-lg px-4 py-2">
                  <span className="text-green-400 font-medium">Free to Play</span>
                </div>
                {userInfo && isTracking && (
                  <div className="bg-yellow-600/20 rounded-lg px-4 py-2">
                    <span className="text-yellow-400 font-medium">⏱️ Tracking Playtime</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameDetail;

