
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Trophy, Medal, Award, Play } from 'lucide-react';
import { useLeaderboard } from '@/hooks/useLeaderboard';
import { cn } from '@/lib/utils';

const games = [
  {
    id: 1,
    title: "Crazy Dummy Swing Multiplayer",
    description: "Swing through levels with physics-based gameplay in this multiplayer adventure!",
    coverImage: "https://imgs.crazygames.com/games/crazy-dummy-swing-multiplayer/cover_16x9-1747406523876.png?metadata=none&quality=85&width=273&fit=crop",
    embedUrl: "https://www.crazygames.com/embed/crazy-dummy-swing-multiplayer"
  },
  {
    id: 2,
    title: "Geoguessr",
    description: "Guess where you are in this addictive geography game!",
    coverImage: "https://imgs.crazygames.com/worldguessr_16x9/20241018082520/worldguessr_16x9-cover?metadata=none&quality=85&width=273&fit=crop",
    embedUrl: "https://www.crazygames.com/embed/worldguessr"
  }
];

const Index = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);
  const [userInfo, setUserInfo] = useState<{ username?: string; id?: string } | null>(null);
  const { data: leaderboardData, isLoading, error } = useLeaderboard();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const username = urlParams.get('username');
    const id = urlParams.get('id');
    
    if (username && id) {
      setUserInfo({ username, id });
    } else {
      setShowModal(true);
    }
  }, [location]);

  const handleGameClick = (gameId: number) => {
    const currentParams = new URLSearchParams(location.search);
    const gameUrl = `/game/${gameId}?fullscreen=true&${currentParams.toString()}`;
    navigate(gameUrl);
  };

  const getInitials = (displayName: string) => {
    const parts = displayName.split(' ');
    if (parts.length > 1) {
      return (parts[0][0] + parts[1][0]).toUpperCase().slice(0,2);
    }
    return displayName.substring(0, 2).toUpperCase();
  };
  
  const formatPlaytime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  // Limit to top 10 players
  const top10Players = leaderboardData?.slice(0, 10) || [];

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#0C0D14' }}>
      {/* Header */}
      <div className="pt-8 pb-6 text-center">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          Gaming Hub
        </h1>
        {userInfo && (
          <p className="text-green-400 mt-2">Welcome back, {userInfo.username}! 🎮</p>
        )}
      </div>

      <div className="container mx-auto px-4 pb-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Games Section */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
              <div className="w-1 h-6 bg-gradient-to-b from-purple-400 to-blue-400 rounded-full"></div>
              Featured Games
            </h2>
            
            <div className="space-y-6">
              {games.map((game) => (
                <Card 
                  key={game.id}
                  className="group cursor-pointer overflow-hidden bg-gray-800/50 border-gray-700 hover:border-purple-500/50 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20 rounded-2xl relative"
                  onClick={() => handleGameClick(game.id)}
                >
                  <div className="flex flex-col md:flex-row h-full">
                    {/* Image Section */}
                    <div className="md:w-80 aspect-video md:aspect-auto relative overflow-hidden">
                      <img 
                        src={game.coverImage}
                        alt={game.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      
                      {/* Play Button Overlay */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <div className="bg-purple-600 hover:bg-purple-700 rounded-full p-4 transform scale-75 group-hover:scale-100 transition-transform duration-300">
                          <Play className="w-8 h-8 text-white fill-white" />
                        </div>
                      </div>
                    </div>
                    
                    {/* Content Section */}
                    <div className="flex-1 p-6 flex flex-col justify-center">
                      <h3 className="text-white font-bold text-2xl mb-3 group-hover:text-purple-300 transition-colors duration-300">
                        {game.title}
                      </h3>
                      <p className="text-gray-300 text-base leading-relaxed">
                        {game.description}
                      </p>
                      
                      {/* Tags */}
                      <div className="mt-4 flex flex-wrap gap-2">
                        <span className="bg-purple-600/20 text-purple-400 px-3 py-1 rounded-full text-sm font-medium">
                          Multiplayer
                        </span>
                        <span className="bg-blue-600/20 text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                          Browser Game
                        </span>
                        <span className="bg-green-600/20 text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                          Free to Play
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Leaderboard Section */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
              <div className="w-1 h-6 bg-gradient-to-b from-yellow-400 to-orange-400 rounded-full"></div>
              Top 10 Leaderboard
            </h2>

            <Card className="bg-gradient-to-b from-gray-800/90 to-gray-900/90 border-gray-600/30 rounded-2xl p-0 overflow-hidden backdrop-blur-sm">
              {isLoading ? (
                <div className="text-center text-gray-400 py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400 mx-auto mb-4"></div>
                  <p>Loading leaderboard...</p>
                </div>
              ) : error ? (
                <div className="text-center text-red-400 py-12">
                  <p>Error loading leaderboard</p>
                </div>
              ) : !top10Players || top10Players.length === 0 ? (
                <div className="text-center text-gray-400 py-12 px-6">
                  <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">No players yet!</p>
                  <p className="text-sm">Be the first to play and earn your spot on the leaderboard!</p>
                </div>
              ) : (
                <div className="p-6">
                  {/* Podium for top players (1-3, or however many exist) */}
                  {top10Players.length > 0 && (
                    <div className="mb-8">
                      <div className="flex items-end justify-center gap-3 mb-6">
                        {/* 2nd Place - only show if exists */}
                        {top10Players.length >= 2 && (
                          <div className="flex flex-col items-center">
                            <div className="relative mb-3">
                              <div className="w-14 h-14 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold text-sm border-3 border-gray-300 shadow-lg">
                                {getInitials(top10Players[1].display_name)}
                              </div>
                              <div className="absolute -top-2 -right-2 bg-gray-800/90 rounded-full p-1.5">
                                <Medal className="w-5 h-5 text-gray-300" />
                              </div>
                            </div>
                            <div className="w-20 h-16 bg-gradient-to-t from-gray-600 to-gray-400 rounded-t-lg flex items-start justify-center pt-2 shadow-lg border-t-2 border-gray-300">
                              <span className="text-white font-bold text-sm">#2</span>
                            </div>
                            <div className="text-center mt-3 max-w-[80px]">
                              <p className="text-white text-xs font-medium truncate">{top10Players[1].display_name}</p>
                              <p className="text-gray-300 text-xs font-semibold">{formatPlaytime(top10Players[1].total_playtime_seconds)}</p>
                            </div>
                          </div>
                        )}

                        {/* 1st Place - always show if any players exist */}
                        <div className="flex flex-col items-center">
                          <div className="relative mb-3">
                            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center text-white font-bold text-lg border-3 border-yellow-300 shadow-xl">
                              {getInitials(top10Players[0].display_name)}
                            </div>
                            <div className="absolute -top-3 -right-3 bg-yellow-600/90 rounded-full p-2">
                              <Trophy className="w-6 h-6 text-yellow-200" />
                            </div>
                          </div>
                          <div className="w-20 h-20 bg-gradient-to-t from-yellow-600 to-yellow-400 rounded-t-lg flex items-start justify-center pt-2 shadow-xl border-t-2 border-yellow-300">
                            <span className="text-white font-bold text-lg">#1</span>
                          </div>
                          <div className="text-center mt-3 max-w-[80px]">
                            <p className="text-white text-sm font-bold truncate">{top10Players[0].display_name}</p>
                            <p className="text-yellow-400 text-xs font-bold">{formatPlaytime(top10Players[0].total_playtime_seconds)}</p>
                          </div>
                        </div>

                        {/* 3rd Place - only show if exists */}
                        {top10Players.length >= 3 && (
                          <div className="flex flex-col items-center">
                            <div className="relative mb-3">
                              <div className="w-14 h-14 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex items-center justify-center text-white font-bold text-sm border-3 border-amber-500 shadow-lg">
                                {getInitials(top10Players[2].display_name)}
                              </div>
                              <div className="absolute -top-2 -right-2 bg-amber-800/90 rounded-full p-1.5">
                                <Award className="w-5 h-5 text-amber-400" />
                              </div>
                            </div>
                            <div className="w-20 h-12 bg-gradient-to-t from-amber-700 to-amber-500 rounded-t-lg flex items-start justify-center pt-2 shadow-lg border-t-2 border-amber-400">
                              <span className="text-white font-bold text-sm">#3</span>
                            </div>
                            <div className="text-center mt-3 max-w-[80px]">
                              <p className="text-white text-xs font-medium truncate">{top10Players[2].display_name}</p>
                              <p className="text-amber-400 text-xs font-semibold">{formatPlaytime(top10Players[2].total_playtime_seconds)}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Rest of leaderboard (4-10) */}
                  {top10Players.length > 3 && (
                    <div className="space-y-3">
                      {top10Players.slice(3).map((player, index) => {
                        const overallRank = 3 + index;
                        
                        return (
                          <div 
                            key={player.user_id} 
                            className={cn(
                              "flex items-center gap-4 p-4 rounded-xl bg-gray-700/40 hover:bg-gray-700/60 transition-all duration-200 border border-gray-600/30 hover:border-gray-500/50"
                            )}
                          >
                            <div className="w-8 text-center">
                              <span className="text-gray-300 font-bold text-sm">#{overallRank + 1}</span>
                            </div>
                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-sm shadow-lg">
                              {getInitials(player.display_name)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-white font-semibold text-sm truncate">{player.display_name}</p>
                              <p className="text-gray-400 text-xs">{player.games_played} games played</p>
                            </div>
                            <div className="text-right">
                              <p className="text-purple-400 font-bold text-sm">{formatPlaytime(player.total_playtime_seconds)}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      {/* Modal for missing user info */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white rounded-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-center">
              Welcome to Gaming Hub!
            </DialogTitle>
            <DialogDescription className="text-gray-300 text-center">
              You're browsing as a guest. To participate in leaderboard rankings and track your progress, 
              please visit with a username and ID in the URL parameters.
              <br /><br />
              <span className="text-sm text-purple-400">
                Example: ?username=YourName&id=123
              </span>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
