
-- Create a table to store user playtime data
CREATE TABLE public.user_playtime (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  game_id INTEGER NOT NULL,
  session_start TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  session_end TIMESTAMP WITH TIME ZONE,
  total_seconds INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create a view for leaderboard data (fixed column reference)
CREATE VIEW public.leaderboard AS
SELECT 
  u.id as user_id,
  u.raw_user_meta_data ->> 'username' as username,
  COALESCE(u.raw_user_meta_data ->> 'username', 'Player' || substring(u.id::text, 1, 8)) as display_name,
  COALESCE(SUM(up.total_seconds), 0) as total_playtime_seconds,
  COUNT(DISTINCT up.game_id) as games_played,
  ROUND(COALESCE(SUM(up.total_seconds), 0) / 60.0, 1) as total_playtime_minutes
FROM auth.users u
LEFT JOIN public.user_playtime up ON u.id = up.user_id
GROUP BY u.id, u.raw_user_meta_data
ORDER BY total_playtime_seconds DESC;

-- Add Row Level Security (RLS)
ALTER TABLE public.user_playtime ENABLE ROW LEVEL SECURITY;

-- Create policies for user_playtime table
CREATE POLICY "Users can view all playtime data" 
  ON public.user_playtime 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can insert their own playtime" 
  ON public.user_playtime 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own playtime" 
  ON public.user_playtime 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Create function to start a play session
CREATE OR REPLACE FUNCTION public.start_play_session(p_game_id INTEGER)
RETURNS UUID AS $$
DECLARE
  session_id UUID;
BEGIN
  INSERT INTO public.user_playtime (user_id, game_id, session_start)
  VALUES (auth.uid(), p_game_id, now())
  RETURNING id INTO session_id;
  
  RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to end a play session
CREATE OR REPLACE FUNCTION public.end_play_session(p_session_id UUID)
RETURNS VOID AS $$
DECLARE
  session_duration INTEGER;
BEGIN
  UPDATE public.user_playtime 
  SET 
    session_end = now(),
    total_seconds = EXTRACT(EPOCH FROM (now() - session_start))::INTEGER,
    updated_at = now()
  WHERE id = p_session_id AND user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
