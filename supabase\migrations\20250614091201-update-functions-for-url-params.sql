
-- Update the start_play_session function to accept user_id parameter
CREATE OR REPLACE FUNCTION public.start_play_session(p_user_id TEXT, p_game_id INTEGER)
RETURNS UUID AS $$
DECLARE
  session_id UUID;
BEGIN
  INSERT INTO public.user_playtime (user_id, game_id, session_start)
  VALUES (p_user_id::UUID, p_game_id, now())
  RETURNING id INTO session_id;
  
  RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the end_play_session function to accept user_id parameter
CREATE OR REPLACE FUNCTION public.end_play_session(p_session_id UUID, p_user_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE public.user_playtime 
  SET 
    session_end = now(),
    total_seconds = EXTRACT(EPOCH FROM (now() - session_start))::INTEGER,
    updated_at = now()
  WHERE id = p_session_id AND user_id = p_user_id::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the leaderboard view to use a simpler approach
DROP VIEW IF EXISTS public.leaderboard;

CREATE VIEW public.leaderboard AS
SELECT 
  up.user_id,
  up.user_id::text as username,
  'Player' || substring(up.user_id::text, 1, 8) as display_name,
  COALESCE(SUM(up.total_seconds), 0) as total_playtime_seconds,
  COUNT(DISTINCT up.game_id) as games_played,
  ROUND(COALESCE(SUM(up.total_seconds), 0) / 60.0, 1) as total_playtime_minutes
FROM public.user_playtime up
GROUP BY up.user_id
ORDER BY total_playtime_seconds DESC;

-- Update RLS policies to allow operations without authentication
DROP POLICY IF EXISTS "Users can view all playtime data" ON public.user_playtime;
DROP POLICY IF EXISTS "Users can insert their own playtime" ON public.user_playtime;
DROP POLICY IF EXISTS "Users can update their own playtime" ON public.user_playtime;

-- Create new policies that allow operations without authentication
CREATE POLICY "Allow all operations on user_playtime"
  ON public.user_playtime
  FOR ALL
  USING (true)
  WITH CHECK (true);
