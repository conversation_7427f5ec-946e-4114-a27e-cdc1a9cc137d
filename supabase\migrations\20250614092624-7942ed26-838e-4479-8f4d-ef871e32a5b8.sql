
-- Update the start_play_session function to accept user_id parameter
CREATE OR REPLACE FUNCTION public.start_play_session(p_user_id TEXT, p_game_id INTEGER)
RETURNS UUID AS $$
DECLARE
  session_id UUID;
BEGIN
  INSERT INTO public.user_playtime (user_id, game_id, session_start)
  VALUES (p_user_id::UUID, p_game_id, now())
  RETURNING id INTO session_id;
  
  RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the end_play_session function to accept user_id parameter
CREATE OR REPLACE FUNCTION public.end_play_session(p_session_id UUID, p_user_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE public.user_playtime 
  SET 
    session_end = now(),
    total_seconds = EXTRACT(EPOCH FROM (now() - session_start))::INTEGER,
    updated_at = now()
  WHERE id = p_session_id AND user_id = p_user_id::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
