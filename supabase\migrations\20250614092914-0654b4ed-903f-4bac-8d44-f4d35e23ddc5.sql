
-- Remove the foreign key constraint from user_playtime.user_id
-- First, we need to find the name of the constraint.
-- It's typically named like user_playtime_user_id_fkey, but let's be sure.
-- If the following commands fail due to constraint name, I can adjust.

-- Attempt to drop the constraint assuming the default naming convention
ALTER TABLE public.user_playtime
DROP CONSTRAINT IF EXISTS user_playtime_user_id_fkey;

-- If the above fails, it might be because the constraint name is different.
-- The Supabase dashboard or an introspective query would confirm the exact name.
-- For now, this is the most likely name based on standard conventions.
