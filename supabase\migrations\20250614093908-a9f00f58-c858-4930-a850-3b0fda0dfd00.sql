
-- Update the start_play_session function to first close any existing open sessions
-- for the same user and game, then start a new one.
CREATE OR REPLACE FUNCTION public.start_play_session(p_user_id TEXT, p_game_id INTEGER)
RETURNS UUID AS $$
DECLARE
  session_id UUID;
  v_user_uuid UUID;
BEGIN
  -- Convert p_user_id to UUID once
  v_user_uuid := p_user_id::UUID;

  -- Close any existing open sessions for this user and game
  UPDATE public.user_playtime
  SET 
    session_end = now(),
    total_seconds = EXTRACT(EPOCH FROM (now() - session_start))::INTEGER,
    updated_at = now()
  WHERE 
    user_id = v_user_uuid AND 
    game_id = p_game_id AND 
    session_end IS NULL;

  -- Insert the new play session
  INSERT INTO public.user_playtime (user_id, game_id, session_start)
  VALUES (v_user_uuid, p_game_id, now())
  RETURNING id INTO session_id;
  
  RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
