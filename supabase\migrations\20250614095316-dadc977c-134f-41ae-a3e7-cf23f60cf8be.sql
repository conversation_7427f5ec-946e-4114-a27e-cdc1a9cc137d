
-- Step 1: Add a column to user_playtime to store the actual username from the URL
ALTER TABLE public.user_playtime
ADD COLUMN IF NOT EXISTS actual_username TEXT;

-- Step 2: Update the start_play_session function to accept and store the actual_username
-- This function previously took (p_user_id TEXT, p_game_id INTEGER)
-- It will now take (p_user_id TEXT, p_actual_username TEXT, p_game_id INTEGER)
CREATE OR REPLACE FUNCTION public.start_play_session(p_user_id TEXT, p_actual_username TEXT, p_game_id INTEGER)
RETURNS UUID AS $$
DECLARE
  session_id UUID;
  v_user_uuid UUID;
BEGIN
  -- Convert p_user_id to UUID (it's the string generated from URL 'id' param)
  v_user_uuid := p_user_id::UUID;

  -- Close any existing open sessions for this user and game
  UPDATE public.user_playtime
  SET 
    session_end = now(),
    total_seconds = EXTRACT(EPOCH FROM (now() - session_start))::INTEGER,
    updated_at = now()
  WHERE 
    user_id = v_user_uuid AND 
    game_id = p_game_id AND 
    session_end IS NULL;

  -- Insert the new play session, now including actual_username
  INSERT INTO public.user_playtime (user_id, actual_username, game_id, session_start)
  VALUES (v_user_uuid, p_actual_username, p_game_id, now())
  RETURNING id INTO session_id;
  
  RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Update the leaderboard view
-- It will now use the actual_username and format display_name as "Firstname Lastname, ID_prefix-"
DROP VIEW IF EXISTS public.leaderboard;
CREATE VIEW public.leaderboard AS
SELECT
  up.user_id,
  -- Keep the existing 'username' column (raw or semi-raw actual_username)
  COALESCE(MAX(up.actual_username), 'Player-' || substring(up.user_id::text, 1, 8)) AS username,
  -- Construct the new 'display_name'
  -- Format: "Firstname Lastname, ID_prefix-"
  -- Example: "Mohamed Elsaadi, 64-"
  (
    SELECT STRING_AGG(INITCAP(part), ' ')
    FROM UNNEST(STRING_TO_ARRAY(COALESCE(MAX(up.actual_username), 'Unknown.User'), '.')) AS part
  ) || ', ' || substring(up.user_id::text, 1, 2) || '-' AS display_name,
  COALESCE(SUM(up.total_seconds), 0) AS total_playtime_seconds,
  COUNT(DISTINCT up.game_id) AS games_played,
  ROUND(COALESCE(SUM(up.total_seconds), 0) / 60.0, 1) AS total_playtime_minutes
FROM
  public.user_playtime up
GROUP BY
  up.user_id  -- Group by user_id to aggregate stats
ORDER BY
  total_playtime_seconds DESC;
