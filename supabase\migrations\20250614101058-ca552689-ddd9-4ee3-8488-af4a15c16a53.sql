
-- Update the leaderboard view for the new display_name format
DROP VIEW IF EXISTS public.leaderboard;
CREATE VIEW public.leaderboard AS
SELECT
  up.user_id,
  -- Keep the existing 'username' column (raw or semi-raw actual_username)
  COALESCE(MAX(up.actual_username), 'Player-' || substring(up.user_id::text, 1, 8)) AS username,
  -- Construct the new 'display_name'
  -- Format: "Firstname Lastname, ID_prefix-"
  -- Example: "<PERSON>, 64-"
  (
    SELECT STRING_AGG(INITCAP(part), ' ')
    FROM UNNEST(STRING_TO_ARRAY(COALESCE(MAX(up.actual_username), 'Unknown.User'), '.')) AS part
  ) || ', ' || substring(up.user_id::text, 1, 2) || '-' AS display_name,
  COALESCE(SUM(up.total_seconds), 0) AS total_playtime_seconds,
  COUNT(DISTINCT up.game_id) AS games_played,
  ROUND(COALESCE(SUM(up.total_seconds), 0) / 60.0, 1) AS total_playtime_minutes
FROM
  public.user_playtime up
GROUP BY
  up.user_id
ORDER BY
  total_playtime_seconds DESC;
